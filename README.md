# LearnPress Mobile App Plugin

## Features
- Adds REST API endpoints for mobile app integration with LearnPress LMS
- Supports in-app purchases for courses (Apple and Google Play)
- Enables push notifications via Firebase for course events and announcements
- Provides social login (Facebook, Google, Apple) for mobile users
- Device registration and management for push notifications
- Secure JWT-based authentication for mobile clients
- Automatic user registration/login via social platforms
- Device token management with automatic cleanup (2-month inactivity)
- Firebase Cloud Messaging integration for cross-platform notifications
- Apple Sign-In with JWT verification using Apple's public keys
- Google OAuth2 token verification
- Facebook Graph API integration for user authentication
- Custom username generation for social login users
- JWT token generation with configurable expiration (default: 1 week)
- Platform-specific notification handling (iOS APNS, Android FCM)

## API Endpoints

### In-App Purchase
- `GET /wp-json/learnpress/v1/mobile-app/product-iap` or `GET /wp-json/lp/v1/mobile-app/product-iap`
  - Returns a list of course IDs available for in-app purchase
  - Response: Array of course ID strings

### Social Login
- `POST /wp-json/lp/v1/mobile-app/verify-facebook` 
  - Body: `{ "token": "facebook_access_token" }`
  - Verifies Facebook access token and returns user data with JWT
- `POST /wp-json/lp/v1/mobile-app/verify-google` 
  - Body: `{ "idToken": "google_id_token" }`
  - Verifies Google ID token and returns user data with JWT
- `POST /wp-json/lp/v1/mobile-app/verify-apple` 
  - Body: `{ "identityToken": "apple_identity_token" }`
  - Verifies Apple Sign-In token using Apple's public keys and returns user data with JWT
- `GET /wp-json/lp/v1/mobile-app/enable-social`
  - Returns boolean indicating if social login is enabled

### Social Login Response Format
All social login endpoints return:
```json
{
  "token": "jwt_token",
  "user_id": 123,
  "user_login": "username",
  "user_email": "<EMAIL>",
  "user_display_name": "User Name"
}
```

### Push Notifications
- `POST /wp-json/learnpress/v1/push-notifications/register-device`
  - Body: `{ "device_token": "fcm_token", "device_type": "ios|android" }`
  - Requires user authentication
  - Registers device for push notifications
- `POST /wp-json/learnpress/v1/push-notifications/delete-device`
  - Body: `{ "device_token": "fcm_token" }`
  - Removes device from notification list
- `POST /wp-json/learnpress/v1/push-notifications/send-notification`
  - Body: `{ "device_token": "fcm_token", "title": "Notification Title", "body": "Notification Body" }`
  - Requires admin privileges
  - Sends push notification via Firebase
- `GET /wp-json/learnpress/v1/push-notifications/get-notifications`
  - Returns list of notifications (currently returns static data)

## Prerequisites
- WordPress with LearnPress plugin (version 4.2.5.7 or later)
- Google Play and Apple developer accounts for in-app purchases
- Firebase project for push notifications
- Facebook and Google developer credentials for social login
- Apple Developer account for Apple Sign-In
- Google Cloud Console project for service account authentication
- SSL certificate (HTTPS) for secure API communication

## Setup

### 1. Plugin Installation
- Install and activate LearnPress plugin first
- Install and activate this mobile app plugin
- Ensure LearnPress version 4.2.5.7+ is installed

### 2. In-App Purchase Configuration
Navigate to LearnPress > Settings > Mobile App > In App Purchase:
- **Courses In App Purchase**: Enter comma-separated course IDs (e.g., "1,2,3")
- **Enable Apple Sandbox**: Check for testing environment
- **Apple Shared Secret**: Add your App Store Connect shared secret
- **Google Play Service Account JSON**: Upload your Google Cloud service account JSON file

### 3. Push Notifications Configuration
In LearnPress > Settings > Mobile App > Push Notifications:
- **Enable Push Notifications**: Check to enable
- **Firebase Project ID**: Enter your Firebase project ID
- **Firebase Service Account JSON**: Upload your Firebase service account JSON file

### 4. Social Login Configuration
In LearnPress > Settings > Mobile App > Social Login:
- **Enable Social Login**: Check to enable
- **Facebook Client ID**: Add your Facebook app client ID
- **Facebook Client Secret**: Add your Facebook app client secret
- **Google Web Client ID**: Add your Google OAuth2 web client ID

## Connecting to Flutterflow

### 1. API Integration
- Use Flutterflow's API calls to connect to the WordPress REST endpoints
- Base URL: `https://your-wordpress-site.com/wp-json/`
- All endpoints are public except push notification registration (requires authentication)

### 2. Authentication Flow
1. Implement social login in your Flutter app (Facebook, Google, Apple)
2. Send the social login token to the corresponding verification endpoint
3. Store the returned JWT token for subsequent API calls
4. Include JWT token in Authorization header: `Bearer <jwt_token>`

### 3. Push Notifications Setup
1. Configure Firebase in your Flutter app
2. After user login, register device token:
   ```
   POST /wp-json/learnpress/v1/push-notifications/register-device
   Body: { "device_token": "fcm_token", "device_type": "ios" }
   ```
3. Handle incoming notifications using Firebase Cloud Messaging

### 4. In-App Purchase Integration
1. Fetch available courses: `GET /wp-json/learnpress/v1/mobile-app/product-iap`
2. Implement in-app purchase in your Flutter app
3. Validate purchases using the configured Apple/Google credentials

### 5. Error Handling
- All endpoints return standard HTTP status codes
- Error responses include descriptive messages
- Handle 403 errors for permission issues
- Handle 500 errors for server-side issues

## Technical Details

### JWT Token Structure
```json
{
  "iss": "https://your-site.com",
  "iat": 1234567890,
  "nbf": 1234567890,
  "exp": 1234567890,
  "data": {
    "user": {
      "id": 123
    }
  }
}
```

### Database Tables
- `wp_learnpress_push_notifications_devices`: Stores device tokens and user associations
- Automatic cleanup of inactive devices (2+ months)

### Security Features
- JWT tokens signed with WordPress SECURE_AUTH_KEY
- Apple Sign-In verification using Apple's public key infrastructure
- Google OAuth2 token verification
- Facebook Graph API token validation
- Automatic username generation to prevent conflicts
- Device token validation and cleanup

### Platform Support
- iOS: APNS payload with content-available and mutable-content flags
- Android: High priority FCM messages
- Cross-platform Firebase Cloud Messaging

## Notes
- All API endpoints are public except push notification registration
- Ensure your WordPress site uses HTTPS for secure communication
- JWT tokens expire after 1 week by default (configurable via filters)
- Device tokens are automatically cleaned up after 2 months of inactivity
- Social login automatically creates WordPress users if they don't exist
- For detailed LearnPress documentation, visit: https://docs.thimpress.com/eduma-app-documentation/ 